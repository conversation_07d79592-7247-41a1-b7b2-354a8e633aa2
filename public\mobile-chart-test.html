<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>手机端图表测试</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 100%;
            margin: 0 auto;
        }
        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        .device-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">手机端图表布局测试</h1>
        
        <div class="device-info">
            <p><strong>屏幕宽度:</strong> <span id="screenWidth"></span>px</p>
            <p><strong>设备类型:</strong> <span id="deviceType"></span></p>
        </div>

        <!-- 模拟数据分析图表区域 -->
        <div class="charts-section">
            <div class="section-header">
                <h3>数据分析图表</h3>
            </div>
            <div class="collapsible-content">
                <div class="charts-container" id="chartsContainer">
                    <!-- 1. 达成率环形图 -->
                    <div class="chart-item">
                        <h3>总体达成率</h3>
                        <div class="chart-container-small">
                            <canvas id="achievementChart"></canvas>
                            <div class="chart-label">达成率</div>
                        </div>
                    </div>

                    <!-- 2. 同比增长环形图 -->
                    <div class="chart-item">
                        <h3>同比情况</h3>
                        <div class="chart-container-small">
                            <canvas id="yoyChart"></canvas>
                            <div class="chart-label">同比增长</div>
                        </div>
                    </div>

                    <!-- 3. 环比增长环形图 -->
                    <div class="chart-item">
                        <h3>环比情况</h3>
                        <div class="chart-container-small">
                            <canvas id="momChart"></canvas>
                            <div class="chart-label">环比增长</div>
                        </div>
                    </div>

                    <!-- 4. 产品占比环形图 -->
                    <div class="chart-item">
                        <h3>占比情况</h3>
                        <div class="chart-container-small">
                            <canvas id="shareChart"></canvas>
                            <div class="chart-label">产品占比</div>
                        </div>
                    </div>

                    <!-- 5. 月度趋势图 -->
                    <div class="chart-item chart-item-full">
                        <h3>月度趋势</h3>
                        <div class="chart-container-large">
                            <canvas id="monthlyTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示设备信息
        function updateDeviceInfo() {
            const width = window.innerWidth;
            document.getElementById('screenWidth').textContent = width;
            
            let deviceType = '';
            if (width <= 480) {
                deviceType = '小屏手机';
            } else if (width <= 768) {
                deviceType = '大屏手机/小平板';
            } else if (width <= 1024) {
                deviceType = '平板';
            } else {
                deviceType = '桌面';
            }
            document.getElementById('deviceType').textContent = deviceType;
        }

        // 创建测试图表
        function createTestCharts() {
            const isSmallMobile = window.innerWidth <= 480;
            const isMobile = window.innerWidth <= 768;
            
            let canvasSize;
            if (isSmallMobile) {
                canvasSize = 180;
            } else if (isMobile) {
                canvasSize = 150;
            } else {
                canvasSize = 200;
            }

            // 创建环形图
            const donutCharts = ['achievementChart', 'yoyChart', 'momChart', 'shareChart'];
            const colors = ['#3b82f6', '#22c55e', '#f59e0b', '#ef4444'];
            const values = [75, 85, 65, 90];

            donutCharts.forEach((chartId, index) => {
                const canvas = document.getElementById(chartId);
                if (canvas) {
                    canvas.width = canvasSize;
                    canvas.height = canvasSize;
                    canvas.style.width = canvasSize + 'px';
                    canvas.style.height = canvasSize + 'px';
                    canvas.style.maxWidth = '100%';
                    canvas.style.maxHeight = '100%';

                    const ctx = canvas.getContext('2d');
                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                data: [values[index], 100 - values[index]],
                                backgroundColor: [colors[index], '#e5e7eb'],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: true,
                            cutout: '70%',
                            layout: {
                                padding: isSmallMobile ? 5 : 10
                            },
                            plugins: {
                                legend: { display: false },
                                tooltip: { enabled: false }
                            }
                        }
                    });
                }
            });

            // 创建月度趋势图
            const monthlyCanvas = document.getElementById('monthlyTrendChart');
            if (monthlyCanvas) {
                if (isSmallMobile) {
                    monthlyCanvas.style.height = '250px';
                } else if (isMobile) {
                    monthlyCanvas.style.height = '300px';
                } else {
                    monthlyCanvas.style.height = '400px';
                }

                const ctx = monthlyCanvas.getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            label: '实际销售',
                            data: [120, 150, 180, 140, 200, 160],
                            backgroundColor: '#3b82f6'
                        }, {
                            label: '目标销售',
                            data: [100, 130, 160, 150, 180, 170],
                            backgroundColor: '#fbbf24'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        layout: {
                            padding: isSmallMobile ? 5 : 10
                        },
                        plugins: {
                            legend: {
                                labels: {
                                    font: {
                                        size: isSmallMobile ? 8 : (isMobile ? 10 : 12)
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                ticks: {
                                    font: {
                                        size: isSmallMobile ? 8 : (isMobile ? 10 : 12)
                                    }
                                }
                            },
                            y: {
                                ticks: {
                                    font: {
                                        size: isSmallMobile ? 8 : (isMobile ? 10 : 12)
                                    }
                                }
                            }
                        }
                    }
                });
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceInfo();
            createTestCharts();
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            updateDeviceInfo();
            // 重新创建图表以适应新尺寸
            setTimeout(createTestCharts, 100);
        });
    </script>
</body>
</html>
