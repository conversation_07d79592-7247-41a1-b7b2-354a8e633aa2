<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>手机端图表测试</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 100%;
            margin: 0 auto;
        }
        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        .device-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">手机端图表布局测试</h1>
        
        <div class="device-info">
            <p><strong>屏幕宽度:</strong> <span id="screenWidth"></span>px</p>
            <p><strong>设备类型:</strong> <span id="deviceType"></span></p>
        </div>

        <!-- 模拟数据分析图表区域 -->
        <div class="charts-section">
            <div class="section-header">
                <h3>数据分析图表</h3>
            </div>
            <div class="collapsible-content">
                <div class="charts-container" id="chartsContainer">
                    <!-- 总达成率环形图 -->
                    <div class="chart-item">
                        <h3>总达成率</h3>
                        <div class="achievement-chart-container">
                            <canvas id="achievementDonutChart" width="300" height="300"></canvas>
                            <div class="chart-center-text" id="achievementCenterText">
                                <div class="achievement-rate">0%</div>
                                <div class="achievement-label">达成率</div>
                            </div>
                        </div>
                    </div>

                    <!-- 同期增长率环形图 -->
                    <div class="chart-item">
                        <h3>同期增长率</h3>
                        <div class="yoy-comparison-chart-container">
                            <canvas id="yoyComparisonChart" width="300" height="300"></canvas>
                            <div class="chart-center-text" id="yoyComparisonCenterText">
                                <div class="comparison-ratio">0%</div>
                                <div class="comparison-label">同期增长</div>
                            </div>
                        </div>
                        <div class="chart-data-info" id="yoyDataInfo">
                            <div class="data-item">
                                <span class="data-label">本期:</span>
                                <span class="data-value" id="yoyCurrentValue">¥0</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">同期:</span>
                                <span class="data-value" id="yoyPreviousValue">¥0</span>
                            </div>
                        </div>
                    </div>

                    <!-- 环比增长率环形图 -->
                    <div class="chart-item">
                        <h3>环比增长率</h3>
                        <div class="mom-comparison-chart-container">
                            <canvas id="momComparisonChart" width="300" height="300"></canvas>
                            <div class="chart-center-text" id="momComparisonCenterText">
                                <div class="comparison-ratio">0%</div>
                                <div class="comparison-label">环比增长</div>
                            </div>
                        </div>
                        <div class="chart-data-info" id="momDataInfo">
                            <div class="data-item">
                                <span class="data-label">当期:</span>
                                <span class="data-value" id="momCurrentValue">¥0</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">上期:</span>
                                <span class="data-value" id="momPreviousValue">¥0</span>
                            </div>
                        </div>
                    </div>

                    <!-- 产品占比饼图 -->
                    <div class="chart-item chart-item-pie-large">
                        <h3>产品销售占比</h3>
                        <canvas id="productShareChart" width="400" height="400"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示设备信息
        function updateDeviceInfo() {
            const width = window.innerWidth;
            document.getElementById('screenWidth').textContent = width;
            
            let deviceType = '';
            if (width <= 480) {
                deviceType = '小屏手机';
            } else if (width <= 768) {
                deviceType = '大屏手机/小平板';
            } else if (width <= 1024) {
                deviceType = '平板';
            } else {
                deviceType = '桌面';
            }
            document.getElementById('deviceType').textContent = deviceType;
        }

        // 创建测试图表
        function createTestCharts() {
            const isSmallMobile = window.innerWidth <= 480;
            const isMobile = window.innerWidth <= 768;
            
            let canvasSize;
            if (isSmallMobile) {
                canvasSize = 180;
            } else if (isMobile) {
                canvasSize = 150;
            } else {
                canvasSize = 200;
            }

            // 创建环形图
            const donutCharts = ['achievementDonutChart', 'yoyComparisonChart', 'momComparisonChart'];
            const colors = ['#3b82f6', '#22c55e', '#f59e0b'];
            const values = [75, 85, 65];

            donutCharts.forEach((chartId, index) => {
                const canvas = document.getElementById(chartId);
                if (canvas) {
                    canvas.width = canvasSize;
                    canvas.height = canvasSize;
                    canvas.style.width = canvasSize + 'px';
                    canvas.style.height = canvasSize + 'px';
                    canvas.style.maxWidth = '100%';
                    canvas.style.maxHeight = '100%';

                    const ctx = canvas.getContext('2d');
                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                data: [values[index], 100 - values[index]],
                                backgroundColor: [colors[index], '#e5e7eb'],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: true,
                            cutout: '70%',
                            layout: {
                                padding: isSmallMobile ? 5 : 10
                            },
                            plugins: {
                                legend: { display: false },
                                tooltip: { enabled: false }
                            }
                        }
                    });

                    // 更新中心文本
                    const centerTextId = chartId.replace('Chart', 'CenterText');
                    const centerText = document.getElementById(centerTextId);
                    if (centerText) {
                        const rateElement = centerText.querySelector('.achievement-rate, .comparison-ratio');
                        if (rateElement) {
                            rateElement.textContent = `${values[index]}%`;
                            rateElement.style.color = colors[index];
                        }
                    }
                }
            });

            // 创建产品占比饼图
            const productCanvas = document.getElementById('productShareChart');
            if (productCanvas) {
                const productSize = isSmallMobile ? 250 : 300;
                productCanvas.width = productSize;
                productCanvas.height = productSize;
                productCanvas.style.width = productSize + 'px';
                productCanvas.style.height = productSize + 'px';
                productCanvas.style.maxWidth = '100%';
                productCanvas.style.maxHeight = '100%';

                const ctx = productCanvas.getContext('2d');
                new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: ['产品A', '产品B', '产品C', '产品D'],
                        datasets: [{
                            data: [30, 25, 25, 20],
                            backgroundColor: ['#3b82f6', '#22c55e', '#f59e0b', '#ef4444'],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        layout: {
                            padding: isSmallMobile ? 5 : 10
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'bottom',
                                labels: {
                                    font: {
                                        size: isSmallMobile ? 8 : (isMobile ? 10 : 12)
                                    }
                                }
                            }
                        }
                    }
                });
            }


        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceInfo();
            createTestCharts();
        });

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            updateDeviceInfo();
            // 重新创建图表以适应新尺寸
            setTimeout(createTestCharts, 100);
        });
    </script>
</body>
</html>
