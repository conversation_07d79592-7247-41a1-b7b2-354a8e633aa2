/*
 Navicat Premium Dump SQL

 Source Server         : 1
 Source Server Type    : SQLite
 Source Server Version : 3045000 (3.45.0)
 Source Schema         : main

 Target Server Type    : SQLite
 Target Server Version : 3045000 (3.45.0)
 File Encoding         : 65001

 Date: 13/07/2025 03:06:14
*/

PRAGMA foreign_keys = false;

-- ----------------------------
-- Table structure for salestable
-- ----------------------------
DROP TABLE IF EXISTS "salestable";
CREATE TABLE "salestable" (
  "id" INTEGER PRIMARY KEY AUTOINCREMENT,
  "城市" TEXT,
  "产品编码concat" TEXT,
  "地区产品线" TEXT,
  "备注" TEXT,
  "产品" TEXT,
  "参考价" REAL DEFAULT 0,
  "县域" TEXT,
  "最终客户编码" TEXT,
  "医院" TEXT,
  "新REP" TEXT,
  "原始客户名称终端" TEXT,
  "年份" INTEGER,
  "月份" INTEGER,
  "季度" TEXT,
  "销量盒折算后" REAL DEFAULT 0,
  "销售金额折算后" REAL DEFAULT 0,
  "指标盒折算后" REAL DEFAULT 0,
  "指标金额" REAL DEFAULT 0,
  "上传人" TEXT DEFAULT 'admin',
  "上传时间" TEXT DEFAULT (datetime('now', 'localtime')),
  "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ----------------------------
-- Auto increment value for salestable
-- ----------------------------
UPDATE "sqlite_sequence" SET seq = 7376 WHERE name = 'salestable';

-- ----------------------------
-- Indexes structure for table salestable
-- ----------------------------
CREATE INDEX "idx_原始数据_产品_年份_月份"
ON "salestable" (
  "产品" ASC,
  "年份" ASC,
  "月份" ASC
);
CREATE INDEX "idx_原始数据_城市_年份_月份"
ON "salestable" (
  "城市" ASC,
  "年份" ASC,
  "月份" ASC
);

PRAGMA foreign_keys = true;
