const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, '../data/dashboard.db');

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('连接数据库失败:', err.message);
    } else {
        console.log('成功连接到SQLite数据库');
    }
});

// 初始化数据库表
function initDatabase() {
    // 直接创建默认管理员用户，不创建表
    console.log('数据库初始化完成，使用现有表结构');
    createDefaultUsers();
}

// 创建默认用户
function createDefaultUsers() {
    // 检查是否已有admin用户
    db.get('SELECT username FROM users WHERE username = ?', ['admin'], (err, row) => {
        if (err) {
            console.error('检查admin用户失败:', err.message);
            return;
        }

        if (row) {
            console.log('admin用户已存在，跳过创建');
            return;
        }

        // 创建admin用户
        const insertUser = `
            INSERT INTO users (username, password, email, full_name, role, department)
            VALUES (?, ?, ?, ?, ?, ?)
        `;

        const adminPassword = bcrypt.hashSync('admin123', 10);

        db.run(insertUser, [
            'admin', adminPassword, '<EMAIL>', '系统管理员', 'admin', '管理部'
        ], (err) => {
            if (err) {
                console.error('创建admin用户失败:', err.message);
            } else {
                console.log('admin用户创建成功 (密码: admin123)');
            }
        });
    });
}





// 导出数据库连接
module.exports = { db, initDatabase };
